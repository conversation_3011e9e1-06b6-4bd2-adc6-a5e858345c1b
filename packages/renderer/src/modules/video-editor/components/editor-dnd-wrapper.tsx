import React, { Props<PERSON><PERSON><PERSON>hildren, useCallback, useState, createContext, useContext } from 'react'
import {
  closestCenter,
  DndContext,
  DragEndEvent,
  DragMoveEvent,
  DragStartEvent,
  PointerSensor,
  useDraggable,
  useDroppable,
  useSensor,
  useSensors
} from '@dnd-kit/core'
import { useTimeline } from '@/modules/video-editor/contexts'
import { Overlay, OverlayType } from '@clipnest/remotion-shared/types'
import { TrackType } from '@/modules/video-editor/types'
import { MaterialResource } from '@/types/resources'
import { useQueryMediaCover } from '@/hooks/queries/useQueryMediaCover'
import { FPS, PIXELS_PER_FRAME } from '@/modules/video-editor/constants'
import { generateNewOverlayId, findStoryboardByFromFrame, isOverlayAcceptableByTrack } from '@/modules/video-editor/utils/track-helper'
import { useEditorContext } from '@/modules/video-editor/contexts'
import { GhostElement, IndexableTrack } from '@/modules/video-editor/types'
import { clamp } from 'lodash'
import { SingleOverlayUpdatePayload } from '@/modules/video-editor/contexts/editor/useOverlays'

export enum EditorDraggableTypes {
  TimelineItem = 'TimelineItem',
  Resource = 'Resource'
}

export enum EditorDroppableTypes {
  TimelineTrack = 'TimelineTrack'
}

// Resource 拖拽上下文
interface ResourceDragContextType {
  mousePosition: GhostElement | null
  landingPoint: GhostElement | null
  isResourceDragging: boolean
}

const ResourceDragContext = createContext<ResourceDragContextType>({
  mousePosition: null,
  landingPoint: null,
  isResourceDragging: false
})

export const useResourceDrag = () => useContext(ResourceDragContext)

type PayloadTypeByDraggableType = {
  [EditorDraggableTypes.TimelineItem]: {
    type: EditorDraggableTypes.TimelineItem,
    overlay: Overlay,
    inNarrationOverlay: boolean
    parent?: Overlay,
  }
  [EditorDraggableTypes.Resource]: {
    type: EditorDraggableTypes.Resource,
    resource: MaterialResource.Media
  }
}

type PayloadTypeByDroppableType = {
  [EditorDroppableTypes.TimelineTrack]: {
    type: EditorDroppableTypes.TimelineTrack,
    trackIndex: number
    trackType: TrackType
  }
}

type ValueOf<T> = T[keyof T]
type DraggablePayload = ValueOf<PayloadTypeByDraggableType>
type DroppablePayload = ValueOf<PayloadTypeByDroppableType>

export function useTypedDraggable<T extends EditorDraggableTypes>(
  type: T,
  id: string | number,
  payload: Omit<PayloadTypeByDraggableType[T], 'type'>
) {
  return useDraggable({
    id,
    data: {
      ...payload,
      type
    }
  })
}

export function useTypedDroppable<T extends EditorDroppableTypes>(
  type: T,
  id: string | number,
  payload: Omit<PayloadTypeByDroppableType[T], 'type'>
) {
  return useDroppable({
    id,
    data: {
      ...payload,
      type
    }
  })
}

type TransformedDragEvent<Event extends DragStartEvent | DragEndEvent | DragMoveEvent> = {
  event: Event & { activatorEvent: PointerEvent },
  draggable: DraggablePayload
  droppable?: DroppablePayload
}

function transformDragEvent<Event extends DragStartEvent | DragEndEvent | DragMoveEvent>(
  event: Event
): TransformedDragEvent<Event> {
  const draggable = event.active.data.current as DraggablePayload

  const droppable = 'over' in event
    ? event.over?.data.current as DroppablePayload | undefined
    : undefined

  return { event: event as any, draggable, droppable }
}

type DraggingResourceData = MaterialResource.Media & {
  x: number,
  y: number,
  dx: number,
  dy: number
}

/**
 * 从 MaterialResource.Media 构造临时 Overlay 对象用于拖拽预览
 */
function createTempOverlayFromResource(
  resource: MaterialResource.Media,
  from: number,
  row: number,
  tracks: any[]
): Overlay {
  // 将秒转换为帧数
  const durationInFrames = Math.round((resource.duration || 10) * FPS)

  // 根据资源类型确定 Overlay 类型
  let overlayType: OverlayType
  switch (resource.resType) {
    case MaterialResource.MediaType.VIDEO:
      overlayType = OverlayType.VIDEO
      break
    case MaterialResource.MediaType.AUDIO:
      overlayType = OverlayType.SOUND
      break
    case MaterialResource.MediaType.IMAGE:
      overlayType = OverlayType.STICKER
      break
    default:
      overlayType = OverlayType.VIDEO // 默认为视频类型
  }

  return {
    id: generateNewOverlayId(tracks),
    type: overlayType,
    from,
    durationInFrames,
    height: 100,
    width: 200,
    left: 0,
    top: 0,
    isDragging: false,
    rotation: 0,
    content: resource.fileName,
    src: resource.url || '',
    styles: overlayType === OverlayType.SOUND ? { volume: 1 } : {}
  } as Overlay
}

/**
 * 计算 Resource 拖拽的坐标信息
 */
function calculateResourceDragPosition(
  initialMouseX: number,
  currentMouseX: number,
  timelineGridRef: React.RefObject<HTMLDivElement | null>,
  zoomScale: number
) {
  if (!timelineGridRef.current) return null

  const rect = timelineGridRef.current.getBoundingClientRect()
  const timelineX = currentMouseX - rect.left
  const framePosition = Math.max(0, Math.round(timelineX / (PIXELS_PER_FRAME * zoomScale)))
  const deltaX = currentMouseX - initialMouseX

  return {
    framePosition,
    deltaX,
    timelineX
  }
}

/**
 * 检查资源是否可以拖拽到目标轨道
 */
function isResourceValidForTrack(
  resource: MaterialResource.Media,
  targetTrack: IndexableTrack
): boolean {
  // 创建临时 overlay 用于验证
  const tempOverlay = {
    type: resource.resType === MaterialResource.MediaType.VIDEO ? OverlayType.VIDEO :
          resource.resType === MaterialResource.MediaType.AUDIO ? OverlayType.SOUND :
          resource.resType === MaterialResource.MediaType.IMAGE ? OverlayType.STICKER :
          OverlayType.VIDEO
  }

  return isOverlayAcceptableByTrack(tempOverlay, targetTrack)
}

const DraggingResource: React.FC<DraggingResourceData> = ({ x, y, dy, dx, ...resource }) => {
  const { data: cover } = useQueryMediaCover(resource.cover)

  return (
    <div
      style={{
        position: 'fixed',
        left: x + dx,
        top: y + dy,
        width: 60,
        height: 40,
        backgroundColor: 'red',
        zIndex: 9999, // 确保拖拽元素在最上层
        pointerEvents: 'none' // 避免拖拽元素阻挡鼠标事件
      }}
    >
      {cover && <img src={cover} alt={resource.cover} />}
    </div>
  )
}

export const EditorDndWrapper: React.FC<PropsWithChildren> = ({ children }) => {
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 1
      }
    })
  )

  const {
    handleOverlayDragStart,
    handleOverlayDragMove,
    handleOverlayDragEnd,
    timelineGridRef,
    zoomScale
  } = useTimeline()

  const { tracks, bulkUpdateOverlays } = useEditorContext()

  const [draggingResource, setDraggingResource] = useState<DraggingResourceData | null>(null)

  // Resource 拖拽独立状态管理
  const [resourceDragState, setResourceDragState] = useState<{
    isActive: boolean
    resource: MaterialResource.Media | null
    initialMouseX: number
    mousePosition: any | null
    landingPoint: any | null
    previewOverlaysAdjust: any | null
    tempOverlay: Overlay | null
  }>({
    isActive: false,
    resource: null,
    initialMouseX: 0,
    mousePosition: null,
    landingPoint: null,
    previewOverlaysAdjust: null,
    tempOverlay: null
  })

  // Resource 拖拽处理方法
  const handleResourceDragStart = useCallback((resource: MaterialResource.Media, initialMouseX: number) => {
    setResourceDragState({
      isActive: true,
      resource,
      initialMouseX,
      mousePosition: null,
      landingPoint: null,
      previewOverlaysAdjust: null,
      tempOverlay: null
    })
  }, [])

  const handleResourceDragMove = useCallback((
    currentMouseX: number,
    currentMouseY: number,
    targetTrackIndex?: number
  ) => {
    if (!resourceDragState.isActive || !resourceDragState.resource) return

    const { resource, initialMouseX } = resourceDragState

    // 如果拖拽到时间轴轨道上
    if (targetTrackIndex !== undefined && timelineGridRef.current) {
      const targetTrack = { ...tracks[targetTrackIndex], index: targetTrackIndex }

      // 检查轨道是否支持该资源
      if (!isResourceValidForTrack(resource, targetTrack)) {
        setResourceDragState(prev => ({
          ...prev,
          mousePosition: null,
          landingPoint: null,
          previewOverlaysAdjust: null
        }))
        return
      }

      // 计算拖拽位置
      const positionInfo = calculateResourceDragPosition(
        initialMouseX,
        currentMouseX,
        timelineGridRef,
        zoomScale
      )

      if (!positionInfo) return

      const { framePosition } = positionInfo

      // 创建临时 overlay 用于预览计算
      const tempOverlay = createTempOverlayFromResource(resource, framePosition, targetTrackIndex, tracks)

      // 计算 mousePosition
      const mousePosition: GhostElement = {
        from: framePosition,
        durationInFrames: tempOverlay.durationInFrames,
        row: targetTrackIndex,
        overlay: tempOverlay,
        invalid: false
      }

      // 计算 landingPoint（这里简化处理，实际应该考虑分镜吸附等逻辑）
      const landingPoint: GhostElement = {
        ...mousePosition,
        from: framePosition // 简化处理，实际应该根据分镜轨道调整
      }

      setResourceDragState(prev => ({
        ...prev,
        mousePosition,
        landingPoint,
        tempOverlay
      }))
    } else {
      // 不在轨道上，清除预览
      setResourceDragState(prev => ({
        ...prev,
        mousePosition: null,
        landingPoint: null,
        previewOverlaysAdjust: null
      }))
    }
  }, [resourceDragState, tracks, timelineGridRef, zoomScale])

  const handleResourceDragEnd = useCallback((targetTrackIndex?: number) => {
    if (!resourceDragState.isActive || !resourceDragState.resource || !resourceDragState.tempOverlay) {
      setResourceDragState({
        isActive: false,
        resource: null,
        initialMouseX: 0,
        mousePosition: null,
        landingPoint: null,
        previewOverlaysAdjust: null,
        tempOverlay: null
      })
      return
    }

    // 如果有有效的落点，插入 Overlay
    if (resourceDragState.landingPoint && targetTrackIndex !== undefined) {
      const { resource, landingPoint } = resourceDragState

      // 创建最终的 Overlay
      const finalOverlay = createTempOverlayFromResource(
        resource,
        landingPoint.from,
        targetTrackIndex,
        tracks
      )

      // 检查是否需要设置分镜索引
      const targetTrack = tracks[targetTrackIndex]
      if (!targetTrack.isGlobalTrack) {
        const storyboard = findStoryboardByFromFrame(tracks, landingPoint.from)
        if (storyboard) {
          finalOverlay.storyboardIndex = storyboard.index
        }
      }

      // 插入 Overlay
      const updatePayload: SingleOverlayUpdatePayload = {
        ...finalOverlay,
        targetTrackIndex
      }

      bulkUpdateOverlays([updatePayload])
    }

    // 清理状态
    setResourceDragState({
      isActive: false,
      resource: null,
      initialMouseX: 0,
      mousePosition: null,
      landingPoint: null,
      previewOverlaysAdjust: null,
      tempOverlay: null
    })
  }, [resourceDragState, tracks, bulkUpdateOverlays])

  const handleDragStart = useCallback(
    ({ draggable, event }: TransformedDragEvent<DragStartEvent>) => {
      if (draggable.type === EditorDraggableTypes.TimelineItem) {
        const { overlay } = draggable
        return handleOverlayDragStart(overlay, 'move')
      }

      if (draggable.type === EditorDraggableTypes.Resource) {
        const resource = draggable.resource
        setDraggingResource({
          ...resource,
          x: event.activatorEvent.clientX,
          y: event.activatorEvent.clientY,
          dx: 0,
          dy: 0
        })

        // 启动独立的 Resource 拖拽处理
        handleResourceDragStart(resource, event.activatorEvent.clientX)
      }
    },
    [handleOverlayDragStart, handleResourceDragStart]
  )

  const handleDragMove = useCallback(
    ({ droppable, event, draggable }: TransformedDragEvent<DragMoveEvent>) => {
      if (draggable.type === EditorDraggableTypes.Resource) {
        // 更新拖拽资源的视觉位置
        setDraggingResource(prev => prev ? {
          ...prev,
          dx: event.delta.x,
          dy: event.delta.y
        } : null)

        // 处理时间轴预览
        const currentMouseX = event.activatorEvent.clientX + event.delta.x
        const currentMouseY = event.activatorEvent.clientY + event.delta.y
        const targetTrackIndex = droppable?.type === EditorDroppableTypes.TimelineTrack
          ? droppable.trackIndex
          : undefined

        handleResourceDragMove(currentMouseX, currentMouseY, targetTrackIndex)
      }

      // 处理现有的 TimelineItem 拖拽
      if (draggable.type === EditorDraggableTypes.TimelineItem && droppable && droppable.type === EditorDroppableTypes.TimelineTrack) {
        const { x: deltaX } = event.delta
        const targetTrackIndex = droppable.trackIndex
        return handleOverlayDragMove(deltaX, targetTrackIndex)
      }
    },
    [handleOverlayDragMove, handleResourceDragMove]
  )

  const handleDragEnd = useCallback(
    ({ draggable, droppable }: TransformedDragEvent<DragEndEvent>) => {
      if (draggable.type === EditorDraggableTypes.Resource) {
        // 清理拖拽资源的视觉状态
        setDraggingResource(null)

        // 处理 Resource 拖拽结束
        const targetTrackIndex = droppable?.type === EditorDroppableTypes.TimelineTrack
          ? droppable.trackIndex
          : undefined

        handleResourceDragEnd(targetTrackIndex)
        return
      }

      if (draggable.type === EditorDraggableTypes.TimelineItem) {
        return handleOverlayDragEnd()
      }
    },
    [handleOverlayDragEnd, handleResourceDragEnd]
  )

  return (
    <ResourceDragContext.Provider
      value={{
        mousePosition: resourceDragState.mousePosition,
        landingPoint: resourceDragState.landingPoint,
        isResourceDragging: resourceDragState.isActive
      }}
    >
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={v => handleDragStart(transformDragEvent(v))}
        onDragMove={v => handleDragMove(transformDragEvent(v))}
        onDragEnd={v => handleDragEnd(transformDragEvent(v))}
      >
        {children}

        {draggingResource && (
          <DraggingResource {...draggingResource} />
        )}
      </DndContext>
    </ResourceDragContext.Provider>
  )
}
