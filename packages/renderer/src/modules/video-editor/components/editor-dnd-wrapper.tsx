import React, { Props<PERSON>ith<PERSON>hildren, useCallback, useState } from 'react'
import {
  closestCenter,
  DndContext,
  DragEndEvent,
  DragMoveEvent,
  DragStartEvent,
  PointerSensor,
  useDraggable,
  useDroppable,
  useSensor,
  useSensors
} from '@dnd-kit/core'
import { useTimeline } from '@/modules/video-editor/contexts'
import { Overlay } from '@clipnest/remotion-shared/types'
import { MaterialResource } from '@/types/resources'
import { useQueryMediaCover } from '@/hooks/queries/useQueryMediaCover'

export enum EditorDraggableTypes {
  TimelineItem = 'TimelineItem',
  Resource = 'Resource'
}

export enum EditorDroppableTypes {
  TimelineTrack = 'TimelineTrack'
}

type PayloadTypeByDraggableType = {
  [EditorDraggableTypes.TimelineItem]: {
    type: EditorDraggableTypes.TimelineItem,
    overlay: Overlay,
    inNarrationOverlay: boolean
    parent?: Overlay,
  }
  [EditorDraggableTypes.Resource]: {
    type: EditorDraggableTypes.Resource,
    resource: MaterialResource.Media
  }
}

type PayloadTypeByDroppableType = {
  [EditorDroppableTypes.TimelineTrack]: {
    type: EditorDroppableTypes.TimelineTrack,
    trackIndex: number
    trackType: TrackType
  }
}

type ValueOf<T> = T[keyof T]
type DraggablePayload = ValueOf<PayloadTypeByDraggableType>
type DroppablePayload = ValueOf<PayloadTypeByDroppableType>

export function useTypedDraggable<T extends EditorDraggableTypes>(
  type: T,
  id: string | number,
  payload: Omit<PayloadTypeByDraggableType[T], 'type'>
) {
  return useDraggable({
    id,
    data: {
      ...payload,
      type
    }
  })
}

export function useTypedDroppable<T extends EditorDroppableTypes>(
  type: T,
  id: string | number,
  payload: Omit<PayloadTypeByDroppableType[T], 'type'>
) {
  return useDroppable({
    id,
    data: {
      ...payload,
      type
    }
  })
}

type TransformedDragEvent<Event extends DragStartEvent | DragEndEvent | DragMoveEvent> = {
  event: Event & { activatorEvent: PointerEvent },
  draggable: DraggablePayload
  droppable?: DroppablePayload
}

function transformDragEvent<Event extends DragStartEvent | DragEndEvent | DragMoveEvent>(
  event: Event
): TransformedDragEvent<Event> {
  const draggable = event.active.data.current as DraggablePayload

  const droppable = 'over' in event
    ? event.over?.data.current as DroppablePayload | undefined
    : undefined

  return { event: event as any, draggable, droppable }
}

type DraggingResourceData = MaterialResource.Media & {
  x: number,
  y: number,
  dx: number,
  dy: number
}



const DraggingResource: React.FC<DraggingResourceData> = ({ x, y, dy, dx, ...resource }) => {
  const { data: cover } = useQueryMediaCover(resource.cover)

  return (
    <div
      style={{
        position: 'fixed',
        left: x + dx,
        top: y + dy,
        width: 60,
        height: 40,
        backgroundColor: 'red',
        zIndex: 9999, // 确保拖拽元素在最上层
        pointerEvents: 'none' // 避免拖拽元素阻挡鼠标事件
      }}
    >
      {cover && <img src={cover} alt={resource.cover} />}
    </div>
  )
}

export const EditorDndWrapper: React.FC<PropsWithChildren> = ({ children }) => {
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 1
      }
    })
  )

  const {
    handleOverlayDragStart,
    handleOverlayDragMove,
    handleOverlayDragEnd,
    handleResourceDragStart,
    handleResourceDragMove,
    handleResourceDragEnd
  } = useTimeline()

  const [draggingResource, setDraggingResource] = useState<DraggingResourceData | null>(null)

  const handleDragStart = useCallback(
    ({ draggable, event }: TransformedDragEvent<DragStartEvent>) => {
      if (draggable.type === EditorDraggableTypes.TimelineItem) {
        const { overlay } = draggable
        return handleOverlayDragStart(overlay, 'move')
      }

      if (draggable.type === EditorDraggableTypes.Resource) {
        const resource = draggable.resource
        setDraggingResource({
          ...resource,
          x: event.activatorEvent.clientX,
          y: event.activatorEvent.clientY,
          dx: 0,
          dy: 0
        })

        // 启动 Resource 拖拽处理
        handleResourceDragStart(resource, event.activatorEvent.clientX)
      }
    },
    [handleOverlayDragStart, handleResourceDragStart]
  )

  const handleDragMove = useCallback(
    ({ droppable, event, draggable }: TransformedDragEvent<DragMoveEvent>) => {
      if (draggable.type === EditorDraggableTypes.Resource) {
        // 更新拖拽资源的视觉位置
        setDraggingResource(prev => prev ? {
          ...prev,
          dx: event.delta.x,
          dy: event.delta.y
        } : null)

        // 处理时间轴预览
        const currentMouseX = event.activatorEvent.clientX + event.delta.x
        const currentMouseY = event.activatorEvent.clientY + event.delta.y
        const targetTrackIndex = droppable?.type === EditorDroppableTypes.TimelineTrack
          ? droppable.trackIndex
          : undefined

        handleResourceDragMove(currentMouseX, currentMouseY, targetTrackIndex)
      }

      // 处理现有的 TimelineItem 拖拽
      if (draggable.type === EditorDraggableTypes.TimelineItem && droppable && droppable.type === EditorDroppableTypes.TimelineTrack) {
        const { x: deltaX } = event.delta
        const targetTrackIndex = droppable.trackIndex
        return handleOverlayDragMove(deltaX, targetTrackIndex)
      }
    },
    [handleOverlayDragMove, handleResourceDragMove]
  )

  const handleDragEnd = useCallback(
    ({ draggable, droppable }: TransformedDragEvent<DragEndEvent>) => {
      if (draggable.type === EditorDraggableTypes.Resource) {
        // 清理拖拽资源的视觉状态
        setDraggingResource(null)

        // 处理 Resource 拖拽结束
        const targetTrackIndex = droppable?.type === EditorDroppableTypes.TimelineTrack
          ? droppable.trackIndex
          : undefined

        handleResourceDragEnd(targetTrackIndex)
        return
      }

      if (draggable.type === EditorDraggableTypes.TimelineItem) {
        return handleOverlayDragEnd()
      }
    },
    [handleOverlayDragEnd, handleResourceDragEnd]
  )

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={v => handleDragStart(transformDragEvent(v))}
      onDragMove={v => handleDragMove(transformDragEvent(v))}
      onDragEnd={v => handleDragEnd(transformDragEvent(v))}
    >
      {children}

      {draggingResource && (
        <DraggingResource {...draggingResource} />
      )}
    </DndContext>
  )
}
