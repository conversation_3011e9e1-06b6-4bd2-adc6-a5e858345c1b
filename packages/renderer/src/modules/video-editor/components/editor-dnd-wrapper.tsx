import React, { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>dren, useCallback, useState } from 'react'
import {
  closestCenter,
  DndContext,
  DragEndEvent,
  DragMoveEvent,
  DragStartEvent,
  PointerSensor,
  useDraggable,
  useDroppable,
  useSensor,
  useSensors
} from '@dnd-kit/core'
import { useTimeline } from '@/modules/video-editor/contexts'
import { Overlay, OverlayType } from '@clipnest/remotion-shared/types'
import { TrackType } from '@/modules/video-editor/types'
import { MaterialResource } from '@/types/resources'
import { useQueryMediaCover } from '@/hooks/queries/useQueryMediaCover'
import { FPS, PIXELS_PER_FRAME } from '@/modules/video-editor/constants'
import { generateNewOverlayId } from '@/modules/video-editor/utils/track-helper'
import { useEditorContext } from '@/modules/video-editor/contexts'

export enum EditorDraggableTypes {
  TimelineItem = 'TimelineItem',
  Resource = 'Resource'
}

export enum EditorDroppableTypes {
  TimelineTrack = 'TimelineTrack'
}

type PayloadTypeByDraggableType = {
  [EditorDraggableTypes.TimelineItem]: {
    type: EditorDraggableTypes.TimelineItem,
    overlay: Overlay,
    inNarrationOverlay: boolean
    parent?: Overlay,
  }
  [EditorDraggableTypes.Resource]: {
    type: EditorDraggableTypes.Resource,
    resource: MaterialResource.Media
  }
}

type PayloadTypeByDroppableType = {
  [EditorDroppableTypes.TimelineTrack]: {
    type: EditorDroppableTypes.TimelineTrack,
    trackIndex: number
    trackType: TrackType
  }
}

type ValueOf<T> = T[keyof T]
type DraggablePayload = ValueOf<PayloadTypeByDraggableType>
type DroppablePayload = ValueOf<PayloadTypeByDroppableType>

export function useTypedDraggable<T extends EditorDraggableTypes>(
  type: T,
  id: string | number,
  payload: Omit<PayloadTypeByDraggableType[T], 'type'>
) {
  return useDraggable({
    id,
    data: {
      ...payload,
      type
    }
  })
}

export function useTypedDroppable<T extends EditorDroppableTypes>(
  type: T,
  id: string | number,
  payload: Omit<PayloadTypeByDroppableType[T], 'type'>
) {
  return useDroppable({
    id,
    data: {
      ...payload,
      type
    }
  })
}

type TransformedDragEvent<Event extends DragStartEvent | DragEndEvent | DragMoveEvent> = {
  event: Event & { activatorEvent: PointerEvent },
  draggable: DraggablePayload
  droppable?: DroppablePayload
}

function transformDragEvent<Event extends DragStartEvent | DragEndEvent | DragMoveEvent>(
  event: Event
): TransformedDragEvent<Event> {
  const draggable = event.active.data.current as DraggablePayload

  const droppable = 'over' in event
    ? event.over?.data.current as DroppablePayload | undefined
    : undefined

  return { event: event as any, draggable, droppable }
}

type DraggingResourceData = MaterialResource.Media & {
  x: number,
  y: number,
  dx: number,
  dy: number
}

/**
 * 从 MaterialResource.Media 构造临时 Overlay 对象用于拖拽预览
 */
function createTempOverlayFromResource(
  resource: MaterialResource.Media,
  from: number,
  row: number,
  tracks: any[]
): Overlay {
  // 将秒转换为帧数
  const durationInFrames = Math.round((resource.duration || 10) * FPS)

  // 根据资源类型确定 Overlay 类型
  let overlayType: OverlayType
  switch (resource.resType) {
    case MaterialResource.MediaType.VIDEO:
      overlayType = OverlayType.VIDEO
      break
    case MaterialResource.MediaType.AUDIO:
      overlayType = OverlayType.SOUND
      break
    case MaterialResource.MediaType.IMAGE:
      overlayType = OverlayType.STICKER
      break
    default:
      overlayType = OverlayType.VIDEO // 默认为视频类型
  }

  return {
    id: generateNewOverlayId(tracks),
    type: overlayType,
    from,
    durationInFrames,
    height: 100,
    width: 200,
    left: 0,
    top: 0,
    isDragging: false,
    rotation: 0,
    content: resource.fileName,
    src: resource.url || '',
    styles: overlayType === OverlayType.SOUND ? { volume: 1 } : {}
  } as Overlay
}

const DraggingResource: React.FC<DraggingResourceData> = ({ x, y, dy, dx, ...resource }) => {
  const { data: cover } = useQueryMediaCover(resource.cover)

  return (
    <div
      style={{
        position: 'fixed',
        left: x + dx,
        top: y + dy,
        width: 60,
        height: 40,
        backgroundColor: 'red',
        zIndex: 9999, // 确保拖拽元素在最上层
        pointerEvents: 'none' // 避免拖拽元素阻挡鼠标事件
      }}
    >
      {cover && <img src={cover} alt={resource.cover} />}
    </div>
  )
}

export const EditorDndWrapper: React.FC<PropsWithChildren> = ({ children }) => {
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 1
      }
    })
  )

  const {
    handleOverlayDragStart,
    handleOverlayDragMove,
    handleOverlayDragEnd,
    timelineGridRef,
    mousePosition,
    landingPoint,
    previewOverlaysAdjust,
    zoomScale
  } = useTimeline()

  const { tracks, bulkUpdateOverlays } = useEditorContext()

  const [draggingResource, setDraggingResource] = useState<DraggingResourceData | null>(null)
  const [resourceDragInfo, setResourceDragInfo] = useState<{
    resource: MaterialResource.Media
    initialMouseX: number
    tempOverlay: Overlay | null
  } | null>(null)

  const handleDragStart = useCallback(
    ({ draggable, event }: TransformedDragEvent<DragStartEvent>) => {
      if (draggable.type === EditorDraggableTypes.TimelineItem) {
        const { overlay } = draggable
        return handleOverlayDragStart(overlay, 'move')
      }

      if (draggable.type === EditorDraggableTypes.Resource) {
        const resource = draggable.resource
        setDraggingResource({
          ...resource,
          x: event.activatorEvent.clientX,
          y: event.activatorEvent.clientY,
          dx: 0,
          dy: 0
        })

        // 初始化 Resource 拖拽信息
        setResourceDragInfo({
          resource,
          initialMouseX: event.activatorEvent.clientX,
          tempOverlay: null
        })
      }
    },
    [handleOverlayDragStart]
  )

  const handleDragMove = useCallback(
    ({ droppable, event, draggable }: TransformedDragEvent<DragMoveEvent>) => {
      if (draggable.type === EditorDraggableTypes.Resource) {
        setDraggingResource(prev => prev ? {
          ...prev,
          dx: event.delta.x,
          dy: event.delta.y
        } : null)

        // 当拖拽到时间轴轨道时，启动时间轴预览逻辑
        if (droppable && droppable.type === EditorDroppableTypes.TimelineTrack && resourceDragInfo) {
          const { resource, initialMouseX } = resourceDragInfo
          const targetTrackIndex = droppable.trackIndex
          const targetTrack = tracks[targetTrackIndex]

          // 检查轨道类型是否支持该资源
          const isValidTrack = targetTrack && (
            targetTrack.type === TrackType.VIDEO ||
            targetTrack.type === TrackType.MIXED ||
            (targetTrack.type === TrackType.SOUND && resource.resType === MaterialResource.MediaType.AUDIO) ||
            (targetTrack.type === TrackType.IMAGE && resource.resType === MaterialResource.MediaType.IMAGE)
          )

          if (isValidTrack) {
            // 启动时间轴拖拽预览（仅在第一次进入轨道时）
            if (!resourceDragInfo.tempOverlay && timelineGridRef.current) {
              // 计算初始位置
              const rect = timelineGridRef.current.getBoundingClientRect()
              const currentMouseX = event.activatorEvent.clientX + event.delta.x
              const timelineX = currentMouseX - rect.left
              const framePosition = Math.max(0, Math.round(timelineX / (PIXELS_PER_FRAME * zoomScale)))

              // 创建临时 Overlay 用于预览
              const tempOverlay = createTempOverlayFromResource(resource, framePosition, targetTrackIndex, tracks)

              handleOverlayDragStart(tempOverlay, 'move')
              setResourceDragInfo(prev => prev ? { ...prev, tempOverlay } : null)
            } else if (resourceDragInfo.tempOverlay) {
              // 更新拖拽位置
              const deltaX = event.activatorEvent.clientX + event.delta.x - initialMouseX
              handleOverlayDragMove(deltaX, targetTrackIndex)
            }
          }
        }
      }

      // 处理现有的 TimelineItem 拖拽
      if (draggable.type === EditorDraggableTypes.TimelineItem && droppable && droppable.type === EditorDroppableTypes.TimelineTrack) {
        const { x: deltaX } = event.delta
        const targetTrackIndex = droppable.trackIndex
        return handleOverlayDragMove(deltaX, targetTrackIndex)
      }
    },
    [handleOverlayDragMove, resourceDragInfo, tracks, timelineGridRef, handleOverlayDragStart, zoomScale]
  )

  const handleDragEnd = useCallback(
    ({ draggable, droppable }: TransformedDragEvent<DragEndEvent>) => {
      if (draggable.type === EditorDraggableTypes.Resource) {
        // 清理 Resource 拖拽状态
        setDraggingResource(null)

        if (resourceDragInfo?.tempOverlay && droppable && droppable.type === EditorDroppableTypes.TimelineTrack) {
          // 如果有临时 Overlay 且拖拽到了轨道上，则完成时间轴拖拽
          handleOverlayDragEnd()
        }

        setResourceDragInfo(null)
        return
      }

      if (draggable.type === EditorDraggableTypes.TimelineItem) {
        return handleOverlayDragEnd()
      }
    },
    [handleOverlayDragEnd, resourceDragInfo]
  )

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={v => handleDragStart(transformDragEvent(v))}
      onDragMove={v => handleDragMove(transformDragEvent(v))}
      onDragEnd={v => handleDragEnd(transformDragEvent(v))}
    >
      {children}

      {draggingResource && (
        <DraggingResource {...draggingResource} />
      )}
    </DndContext>
  )
}
