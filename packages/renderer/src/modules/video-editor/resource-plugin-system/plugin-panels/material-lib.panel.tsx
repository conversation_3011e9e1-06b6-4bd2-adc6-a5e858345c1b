import React, { memo, useCallback, useEffect, useState } from 'react'
import { MaterialResource, ResourceSource } from '@/types/resources'
import { Loader2, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useQueryMediaList } from '@/hooks/queries/useQueryMaterial'
import { useQueryClient } from '@tanstack/react-query'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { SortMenu } from '@/pages/Projects/material/components/MaterialFilterBar'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { SearchInput } from '@/components/ui/search-input'
import MediaTypeSelector from '@/pages/Projects/material/components/MediaTypeSelector'
import UploadMaterial from '@/pages/Projects/material/components/UploadMaterial'
import MediaItem, { FolderAction, MediaAction } from '@/pages/Projects/material/components/MediaItem'
import MoveDialog from '@/pages/Projects/material/components/MoveDialog'
import { InfiniteResourceList } from '@/components/InfiniteResourceList'
import Breadcrumbs from '@/components/Breadcrumbs'
import { useItemActions } from '@/hooks/useItemActions'
import { useFolderActions } from '@/hooks/useFolderActions'
import { useMediaActions } from '@/hooks/useMediaActions'
import { useDeleteModal } from '@/components/modal/delete'
import { isValidFolderId, TreeNode } from '@/components/TreeList'
import { useSelection } from '@/hooks/useSelection'
import { useFolderData } from '@/hooks/useFolderData'
import { ResourceModule } from '@/libs/request/api/resource'
import { usePending } from '@/hooks/usePending'
import { useEditorContext } from '@/modules/video-editor/contexts'
import { EditorDraggableTypes, useTypedDraggable } from '@/modules/video-editor/components/editor-dnd-wrapper'

type EditorMediaItemProps = {
  item: MaterialResource.Media
  mediaActions: MediaAction[]
  selectedMediaItems: Set<string>
  toggleSelect: (fileId: string, isFolder: boolean) => void
  onItemAdd: () => void
}

const EditorMediaItem: React.FC<EditorMediaItemProps> = ({
  item,
  mediaActions,
  selectedMediaItems,
  toggleSelect,
  onItemAdd,
}) => {
  const { setNodeRef, listeners, attributes } = useTypedDraggable(
    EditorDraggableTypes.Resource,
    item.fileId,
    {
      resource: item
    }
  )

  return (
    <div
      ref={setNodeRef}
      draggable
      {...listeners}
      {...attributes}
    >
      <MediaItem
        isEditItem={true}
        orientation="horizontal"
        media={item}
        isSelected={selectedMediaItems.has(item.fileId)}
        isFolder={false}
        actions={mediaActions}
        onToggleSelect={fileId => toggleSelect(fileId, false)}
        onItemAdd={onItemAdd}
      />
    </div>
  )
}

const EditorFolderItem = ({
  folder,
  folderActions,
  draggingItem,
  selectedFolderItems,
  toggleSelect,
  handleFolderClick,
  setDraggingItem,
  onRefresh,
  onbatchImportStoryboards, // 批量导入到分镜
}: {
  folder: MaterialResource.Media
  folderActions: FolderAction[]
  draggingItem: {
    id: string
    type: ResourceSource
  } | null

  selectedFolderItems: Set<string>
  toggleSelect: (fileId: string, isFolder: boolean) => void
  handleFolderClick: (folderId: string) => void
  setDraggingItem: React.Dispatch<
    React.SetStateAction<{
      id: string
      type: ResourceSource
    } | null>
  >
  onRefresh: () => Promise<void>
  onbatchImportStoryboards: () => void
}) => {
  return (
    <MediaItem
      key={folder.fileId}
      data-type="folder"
      isEditItem={true}
      orientation="horizontal"
      media={folder}
      isSelected={selectedFolderItems.has(folder.fileId)}
      isFolder={true}
      actions={folderActions}
      onToggleSelect={fileId => toggleSelect(fileId, true)}
      onFolderClick={() => handleFolderClick(folder.fileId)}
      draggable
      onDragStart={() => setDraggingItem({ id: folder.fileId, type: ResourceSource.FOLDER })}
      onDragEnd={() => setDraggingItem(null)}
      onDragOver={e => e.preventDefault()} // 允许放置
      onDrop={async e => {
        e.preventDefault()
        if (!draggingItem || draggingItem.id === folder.fileId) return

        if (draggingItem.type === ResourceSource.MEDIA) {
          // 移动文件到文件夹
          await ResourceModule.media.move({
            fileIds: [draggingItem.id],
            folderUuid: folder.fileId,
          })
        } else if (draggingItem.type === ResourceSource.FOLDER) {
          // 移动文件夹到文件夹
          await ResourceModule.directory.move({
            folderIds: [draggingItem.id],
            parentId: folder.fileId,
          })
        }
        await onRefresh()
      }}
      onbatchImportStoryboards={onbatchImportStoryboards}
    />
  )
}

/**
 * 素材库组件
 */
export function MaterialLibPanel() {
  const { projectId } = useEditorContext()
  const queryClient = useQueryClient()

  const [activeTab, setActiveTab] = useState(0)
  const [moveDialogOpen, setMoveDialogOpen] = useState(false)
  const [moveId, setMoveId] = useState('')
  const [moveType, setMoveType] = useState(ResourceSource.FOLDER)

  const { createItem, renameItem, deleteItem } = useItemActions()
  const folderActions = useFolderActions(createItem, renameItem, deleteItem, setMoveType, setMoveId, setMoveDialogOpen)
  const mediaActions = useMediaActions(renameItem, deleteItem, setMoveType, setMoveId, setMoveDialogOpen)
  const deleteModal = useDeleteModal()

  const [draggingItem, setDraggingItem] = useState<{
    id: string
    type: ResourceSource // MEDIA or FOLDER
  } | null>(null)

  const [filters, setFilters] = useState<MaterialResource.MaterialMediaParams>({
    projectId: Number(projectId),
    folderUuid: '',
    sortField: MaterialResource.SortField.UPLOAD_TIME,
    sortOrder: MaterialResource.SortOrder.ASC,
    createAtRange: [],
    durationRange: [],
    useCountRange: undefined, // 合成次数
    quoteCountRange: undefined, // 引用次数
    keyword: undefined,
    resType: undefined,
  })

  const inputs = [
    { placeholder: '搜索关键词', field: 'keyword' },
    { placeholder: '合成次数', field: 'useCountRange', isNumber: true },
    { placeholder: '引用次数', field: 'quoteCountRange', isNumber: true },
  ]

  const {
    treeData,
    isSuccess: isTreeSuccess,
    currentFolderId,
    setCurrentFolderId,
    folderPath,
    childFolders,
  } = useFolderData(Number(projectId))
  const mediaQueryResult = useQueryMediaList(filters, isTreeSuccess && filters.folderUuid !== '')

  const onRefresh = async () => {
    await Promise.all([
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.MATERIAL_MEDIA_LIST] }),
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.MATERIAL_DIRECTORY_LIST] }),
    ])
  }

  const folderAsMediaItems: MaterialResource.Media[] = childFolders.map(folder => ({
    fileId: folder.id,
    fileName: folder.label,
    folderUuid: folder.raw.parentId,
    childrenFolder: folder.children.length || 0,
    mediaNum: folder.raw.imageCount + folder.raw.videoCount,
    resType: 0, // 代表文件夹
    createTime: folder.raw.createdAt || new Date().toISOString(),
  }))

  const {
    selectedMediaItems,
    selectedFolderItems,
    setSelectedMediaItems,
    setSelectedFolderItems,
    toggleSelect,
    toggleSelectAll,
    allSelected,
    selectedCount,
    mediaCount,
  } = useSelection({
    mediaList: mediaQueryResult.data,
    folderAsMediaItems,
    getMediaId: media => media.fileId,
    getFolderId: folder => folder.fileId,
  })

  const handleMaterialClick = useCallback(async (data: MaterialResource.Media) => {
    try {
      console.log('点击添加到轨道', data)
    } catch (error) {
      console.error('失败:', error)
    }
  }, [])

  const handleBatchImportStoryboards = useCallback(async (data: MaterialResource.Media) => {
    try {
      console.log('批量导入到分镜')
      console.log('点击的文件夹数据', data)
      const res = await ResourceModule.media.list({
        pageSize: 100, // 取足够多的文件
        pageNo: 1,
        projectId: Number(projectId),
        folderUuid: data.fileId,
        sortField: MaterialResource.SortField.UPLOAD_TIME,
        sortOrder: MaterialResource.SortOrder.ASC,
      })

      const folderResources = res?.list || []
      if (folderResources.length > 0) {
        console.log('点击的文件夹下的媒体文件数据', folderResources)
      } else {
        console.log('该文件夹下没有媒体文件资源')
      }
    } catch (error) {
      console.error('获取文件夹媒体文件资源失败:', error)
    }
  }, [])

  // 素材包装组件，处理异步加载状态
  const MediaItemWrapper = useCallback(
    ({ item, index }: { item: MaterialResource.Media; index: number }) => (
      <EditorMediaItem
        key={`media-${item.fileId}-${index}`}
        item={item}
        mediaActions={mediaActions}
        selectedMediaItems={selectedMediaItems}
        toggleSelect={toggleSelect}
        onItemAdd={() => handleMaterialClick(item)}
      />
    ),
    [handleMaterialClick, mediaActions],
  )

  const renderMediaItem = useCallback(
    (item: MaterialResource.Media, index: number) => {
      return <MediaItemWrapper item={item} index={index} />
    },
    [MediaItemWrapper],
  )

  const renderMediaContent = useCallback(() => {
    const folderItems = folderAsMediaItems.map(folder => (
      <EditorFolderItem
        key={`folder-${folder.fileId}`}
        folder={folder}
        folderActions={folderActions}
        draggingItem={draggingItem}
        selectedFolderItems={selectedFolderItems}
        toggleSelect={toggleSelect}
        handleFolderClick={handleFolderClick}
        setDraggingItem={setDraggingItem}
        onRefresh={onRefresh}
        onbatchImportStoryboards={() => handleBatchImportStoryboards(folder)}
      />
    ))
    return (
      <div>
        <InfiniteResourceList
          queryResult={mediaQueryResult}
          renderItem={renderMediaItem}
          emptyText="暂无素材"
          loadingText="加载素材中..."
          itemsContainerClassName="grid grid-cols-4 gap-3 pt-3 pb-3"
          headerContent={folderItems}
        />
      </div>
    )
  }, [mediaQueryResult, renderMediaItem])

  const handleFolderClick = (folderId: string) => {
    setCurrentFolderId(folderId)
    queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.MATERIAL_MEDIA_LIST] })
  }

  const handleMove = async (targetFolderId: string) => {
    // 移动媒体文件
    if (selectedMediaItems.size > 0) {
      await ResourceModule.media.move({
        fileIds: Array.from(selectedMediaItems),
        folderUuid: targetFolderId,
      })
      await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.MATERIAL_MEDIA_LIST] })
    }

    // 移动文件夹
    if (selectedFolderItems.size > 0) {
      await ResourceModule.directory.move({
        folderIds: Array.from(selectedFolderItems),
        parentId: targetFolderId,
      })
      await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.MATERIAL_DIRECTORY_LIST] })
    }

    setSelectedMediaItems(new Set())
    setSelectedFolderItems(new Set())
  }

  const handleMoveConfirm = async (selectedNode: TreeNode) => {
    if (moveType === ResourceSource.MULTI_SELECT) {
      handleMove(selectedNode.id)
    } else {
      await onRefresh()
    }
  }

  //放入回收站/彻底删除
  const handleBatchDelete = async (isPermanent: boolean) => {
    if (selectedMediaItems.size > 0) {
      await ResourceModule.media[isPermanent ? 'delete' : 'recycle']({
        fileIds: [...selectedMediaItems],
      })
    }
    if (selectedFolderItems.size > 0) {
      await ResourceModule.directory[isPermanent ? 'delete' : 'recycle']({
        folderIds: [...selectedFolderItems],
      })
    }
    await onRefresh()
    setSelectedMediaItems(new Set())
    setSelectedFolderItems(new Set())
  }

  useEffect(() => {
    setFilters(prevFilters => ({
      ...prevFilters,
      folderUuid: currentFolderId,
    }))
  }, [currentFolderId])

  useEffect(() => {
    console.log('mediaQueryResult', mediaQueryResult)
  }, [mediaQueryResult])

  useEffect(() => {
    if (!isTreeSuccess || !treeData || treeData.length === 0) return

    const firstFolderId = treeData[0].id

    // 当前无选中目录 或 选中的目录在 treeData 中已不存在
    if (!currentFolderId || !isValidFolderId(treeData, currentFolderId)) {
      setCurrentFolderId(firstFolderId)
      handleFolderClick(firstFolderId)
    }
  }, [isTreeSuccess, treeData, currentFolderId])

  // 刷新选中状态
  useEffect(() => {
    setSelectedMediaItems(new Set())
    setSelectedFolderItems(new Set())
  }, [currentFolderId])

  return (
    <div className="flex flex-col h-full w-full overflow-hidden px-2 py-1">
      <div className="flex justify-between gap-1 py-1">
        {/* 上传 */}
        <UploadMaterial
          folderUuid={filters.folderUuid}
          onUpload={async () => {
            await queryClient.invalidateQueries({
              queryKey: [QUERY_KEYS.MATERIAL_DIRECTORY_LIST],
            })
          }}
        />

        {/* 新建文件夹 */}
        <Button
          variant="ghost"
          size="sm"
          className="px-4 h-7 bg-primary/10 shadow-md"
          onClick={() =>
            createItem(ResourceSource.FOLDER, filters.folderUuid, {
              label: '文件夹名称',
              headerTitle: '文件夹',
            })}
        >
          新建文件夹
        </Button>
      </div>
      <div className="flex justify-between mt-2">
        {/* 下拉列表：树列表 */}
        <Select
          value={filters.folderUuid}
          onValueChange={value => setFilters(prev => ({ ...prev, folderUuid: value }))}
        >
          <SelectTrigger
            className="w-32 mb-2 text-sm border-0 bg-primary/10 h-7 w-full mr-2"
            style={{ fontSize: '12px' }}
          >
            <SelectValue placeholder="项目" />
          </SelectTrigger>
          <SelectContent>
            {treeData?.map(opt => (
              <SelectItem key={opt.id} value={opt.id}>
                {opt.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {/* 排序 */}
        <SortMenu
          sortField={filters.sortField}
          sortOrder={filters.sortOrder}
          size="sm"
          onChange={(field, order) =>
            setFilters(prev => ({
              ...prev,
              sortField: field,
              sortOrder: order,
            }))}
        />
      </div>
      {/* 筛选 */}
      <div className="flex gap-1">
        {inputs.map(({ placeholder, field, isNumber }) => (
          <SearchInput
            key={field}
            placeholder={placeholder}
            value={filters[field] ?? ''}
            onChange={e =>
              setFilters(prev => ({
                ...prev,
                [field]: isNumber ? Number(e.target.value) : e.target.value,
              }))}
            containerClassName="w-30%"
            size="xs"
          />
        ))}
      </div>
      {/* 分类筛选 */}
      <div>
        <MediaTypeSelector activeTab={activeTab} isEdit={true} setActiveTab={setActiveTab} setFilters={setFilters} />
      </div>

      {selectedCount ? (
        <div className="flex items-center justify-between text-sm ">
          <div className="flex justify-end items-center space-x-4">
            <span>总数：{mediaCount}</span>
            <label className="flex items-center cursor-pointer">
              <input
                type="radio"
                checked={allSelected}
                onClick={toggleSelectAll}
                readOnly
                className="accent-primary-highlight1"
              />
            </label>
            <span>已选 {selectedCount}</span>
          </div>
          <div>
            <Button
              variant="link"
              size="sm"
              className="text-primary-highlight1"
              onClick={() => {
                setMoveType(ResourceSource.MULTI_SELECT)
                setMoveDialogOpen(true)
              }}
            >
              移动到
            </Button>
            <span className="text-primary-highlight1"> | </span>
            <Button
              variant="link"
              size="sm"
              className="text-primary-highlight1 "
              onClick={() => {
                deleteModal({
                  kind: '',
                  name: '所选文件',
                  danger: true,
                  buttons: ({ close }) => {
                    const { pending, withPending } = usePending()

                    return (
                      <>
                        <Button
                          variant="default"
                          className="min-w-[80px] h-8 ml-2 bg-white/5 text-white border hover:bg-primary-highlight1"
                          onClick={withPending(async () => {
                            await handleBatchDelete(false)
                            close()
                          })}
                        >
                          {pending ? <Loader2 className="animate-spin size-4" /> : '放入回收站'}
                        </Button>
                        <Button
                          variant="destructive"
                          className="min-w-[80px] h-8 ml-2 bg-destructive text-white border hover:bg-destructive/90"
                          onClick={withPending(async () => {
                            await handleBatchDelete(true)
                            close()
                          })}
                        >
                          {pending ? <Loader2 className="animate-spin size-4" /> : '彻底删除'}
                        </Button>
                      </>
                    )
                  },
                })
              }}
            >
              删除
            </Button>
          </div>
        </div>
      ) : (
        <div className="flex items-center justify-between text-sm text-gray-600">
          <Breadcrumbs folderPath={folderPath} currentFolderId={currentFolderId} onFolderClick={handleFolderClick} />
          <RefreshCw className="inline-block w-4 h-4 mr-1 text-primary-highlight1" onClick={onRefresh} />
        </div>
      )}

      <div className="mt-2">{renderMediaContent()}</div>
      <MoveDialog
        open={moveDialogOpen}
        moveId={moveId}
        moveType={moveType}
        onOpenChange={setMoveDialogOpen}
        onConfirm={handleMoveConfirm}
      />
    </div>
  )
}

export default memo(MaterialLibPanel)
