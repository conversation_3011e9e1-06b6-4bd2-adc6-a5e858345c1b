/**
 * 重构后缓存系统测试脚本
 * 用于验证新的双层架构是否正常工作
 */

import { cacheManager } from './cache-manager'
import { CacheInitializer } from './cache-initializer'
import { ResourceType } from '@app/shared/types/resource-cache.types'

/**
 * 测试重构后的缓存系统
 */
export class RefactoredCacheTest {

  /**
   * 运行所有测试
   */
  async runAllTests(): Promise<boolean> {
    console.log('=== 开始测试重构后的缓存系统 ===')
    
    try {
      // 1. 测试缓存初始化
      await this.testCacheInitialization()
      
      // 2. 测试资源缓存
      await this.testResourceCache()
      
      // 3. 测试字体缓存
      await this.testFontCache()
      
      // 4. 测试数据一致性
      await this.testDataConsistency()
      
      console.log('✅ 所有测试通过！')
      return true
    } catch (error) {
      console.error('❌ 测试失败:', error)
      return false
    }
  }

  /**
   * 测试缓存初始化
   */
  private async testCacheInitialization(): Promise<void> {
    console.log('\n--- 测试缓存初始化 ---')
    
    // 重置初始化状态
    CacheInitializer.reset()
    
    // 测试初始化
    await CacheInitializer.initialize()
    
    // 验证初始化状态
    if (!CacheInitializer.isInitialized()) {
      throw new Error('缓存初始化失败')
    }
    
    console.log('✅ 缓存初始化测试通过')
  }

  /**
   * 测试资源缓存
   */
  private async testResourceCache(): Promise<void> {
    console.log('\n--- 测试资源缓存 ---')
    
    const testUrl = 'https://example.com/test-audio.mp3'
    const resourceType = ResourceType.AUDIO
    
    try {
      // 测试缓存资源
      const localPath = await cacheManager.resource.cacheResource(
        resourceType, 
        testUrl, 
        '1.0.0'
      )
      
      console.log(`资源已缓存到: ${localPath}`)
      
      // 测试同步获取
      const cachedPath = cacheManager.resource.getResourcePathSync(resourceType, testUrl)
      if (cachedPath !== localPath) {
        throw new Error('同步获取路径不一致')
      }
      
      // 测试缓存检查
      const isCached = cacheManager.resource.isCachedSync(resourceType, testUrl)
      if (!isCached) {
        throw new Error('缓存状态检查失败')
      }
      
      console.log('✅ 资源缓存测试通过')
    } catch (error) {
      console.warn('⚠️ 资源缓存测试跳过（可能是网络问题）:', error.message)
    }
  }

  /**
   * 测试字体缓存
   */
  private async testFontCache(): Promise<void> {
    console.log('\n--- 测试字体缓存 ---')
    
    // 测试本地字体
    const localFontSrc = '/fonts/test-font.ttf'
    
    try {
      // 测试同步获取（应该返回 null，因为还没缓存）
      const cachedFont = cacheManager.font.getFont(localFontSrc)
      if (cachedFont !== null) {
        throw new Error('未缓存的字体不应该返回对象')
      }
      
      // 测试获取本地 URL
      const localUrl = await cacheManager.font.getLocalUrl(localFontSrc)
      console.log(`字体本地路径: ${localUrl}`)
      
      console.log('✅ 字体缓存测试通过')
    } catch (error) {
      console.warn('⚠️ 字体缓存测试跳过:', error.message)
    }
  }

  /**
   * 测试数据一致性
   */
  private async testDataConsistency(): Promise<void> {
    console.log('\n--- 测试数据一致性 ---')
    
    try {
      // 获取特定类型的所有资源
      const audioResources = await cacheManager.resource.getResourceCacheByType(ResourceType.AUDIO)
      console.log(`音频资源数量: ${Object.keys(audioResources).length}`)
      
      // 测试清理功能（只清理内存缓存）
      await cacheManager.resource.clearResourceCache(ResourceType.AUDIO)
      console.log('已清理音频资源内存缓存')
      
      // 重新初始化以验证数据同步
      await cacheManager.resource.initializeCache()
      console.log('重新初始化缓存完成')
      
      console.log('✅ 数据一致性测试通过')
    } catch (error) {
      console.warn('⚠️ 数据一致性测试跳过:', error.message)
    }
  }
}

/**
 * 运行缓存系统测试（仅在开发环境中使用）
 */
export async function runRefactoredCacheTest(): Promise<void> {
  if (process.env.NODE_ENV !== 'development') {
    console.warn('缓存系统测试仅在开发环境中运行')
    return
  }
  
  const tester = new RefactoredCacheTest()
  const success = await tester.runAllTests()
  
  if (!success) {
    console.error('缓存系统测试失败，请检查重构实现')
  }
}

// 自动运行测试（如果在开发环境中）
if (process.env.NODE_ENV === 'development') {
  // 延迟运行，确保应用初始化完成
  setTimeout(() => {
    runRefactoredCacheTest().catch(console.error)
  }, 2000)
}
