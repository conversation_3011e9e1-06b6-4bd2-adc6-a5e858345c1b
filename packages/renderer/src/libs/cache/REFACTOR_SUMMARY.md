# 前端缓存系统重构总结

## 重构目标

移除渲染进程中的 IndexedDB 存储层，简化为**主进程 manifest.json + 渲染进程内存缓存**的双层架构，解决数据一致性问题。

## 核心变更

### 1. **移除冗余存储层**

#### ResourceCacheManager
- ❌ 删除 `resourceCacheStore: LocalForage`
- ✅ 保留 `memoryCache: Map<string, ResourceCacheEntry>`
- ✅ 数据结构统一使用 `ResourceCacheEntry`（与主进程一致）

#### FontCacheManager  
- ❌ 删除 `fontCacheStore: LocalForage`
- ✅ 保留 `memoryCache: Map<string, opentype.Font>`
- ✅ 新增 `fontMetadataCache: Map<string, FontMetadata>`

### 2. **统一缓存键策略**

#### 新增工具函数
```typescript
// packages/renderer/src/libs/cache/utils/hash.ts
export function hashUrl(url: string): string
export async function hashUrlAsync(url: string): Promise<string>
```

#### 缓存键对齐
- **之前**: `${type}:${url}` (渲染进程) vs `hashUrl(url)` (主进程)
- **现在**: 统一使用 `hashUrl(url)`

### 3. **初始化同步机制**

#### ResourceCacheManager
```typescript
public async initializeCache(): Promise<void> {
  // 通过 IPC 调用主进程的 getAllResources()
  const allEntries = await window.resource.getAllResources()
  
  // 将主进程数据同步到渲染进程内存缓存
  for (const entry of allEntries) {
    this.memoryCache.set(entry.key, entry)
  }
}
```

#### 缓存初始化器增强
```typescript
// packages/renderer/src/libs/cache/cache-initializer.ts
export class CacheInitializer {
  static async initialize(): Promise<void> {
    // 等待主进程资源缓存系统准备就绪
    await this.waitForMainProcessReady()
    
    // 初始化资源缓存管理器（从主进程同步数据）
    await cacheManager.resource.initializeCache()
  }
}
```

### 4. **统一更新流程**

#### 缓存更新策略
1. **写入**: 只写入主进程 manifest.json
2. **读取**: 优先从渲染进程内存缓存读取
3. **同步**: 通过 IPC 调用后更新内存缓存

#### 示例流程
```typescript
async cacheResource(type: ResourceType, url: string): Promise<string> {
  // 1. 检查内存缓存
  const key = this.getCacheKey(url)
  const existingEntry = this.memoryCache.get(key)
  
  // 2. 通过 IPC 调用主进程
  const localPath = await window.resource.fetchOrSaveResource({
    url, type, version
  })
  
  // 3. 更新内存缓存
  const updatedEntry = await window.resource.getResource({ url })
  if (updatedEntry) {
    this.memoryCache.set(updatedEntry.key, updatedEntry)
  }
  
  return localPath
}
```

### 5. **架构简化**

#### 之前的架构
```
渲染进程: IndexedDB ←→ 内存缓存 ←→ IPC ←→ 主进程: manifest.json + 文件系统
```

#### 重构后的架构
```
渲染进程: 内存缓存 ←→ IPC ←→ 主进程: manifest.json + 文件系统
```

## 文件变更清单

### 修改的文件
- `packages/renderer/src/libs/cache/parts/resource.cache.ts` - 移除 IndexedDB，重构为内存缓存
- `packages/renderer/src/libs/cache/parts/font.cache.ts` - 移除 IndexedDB，重构为内存缓存
- `packages/renderer/src/libs/cache/types.ts` - 使 store 属性可选
- `packages/renderer/src/libs/cache/cache-manager.ts` - 更新构造函数调用
- `packages/renderer/src/libs/cache/cache-initializer.ts` - 增强初始化逻辑

### 新增的文件
- `packages/renderer/src/libs/cache/utils/hash.ts` - 统一的 hash 工具函数

## 预期效果

### ✅ 解决的问题
1. **数据一致性**: 消除双层 IndexedDB 存储带来的不一致风险
2. **缓存键冲突**: 统一使用 `hashUrl()` 策略
3. **版本管理**: 统一由主进程管理版本控制
4. **初始化顺序**: 确保渲染进程在主进程就绪后再初始化

### ✅ 架构优势
1. **单一数据源**: 主进程 manifest.json 作为唯一持久化存储
2. **简化维护**: 减少缓存管理复杂度
3. **提高可靠性**: 避免双层存储同步问题
4. **性能优化**: 内存缓存提供快速访问

### ⚠️ 注意事项
1. **内存使用**: 渲染进程内存缓存会占用更多内存
2. **初始化时间**: 首次启动需要从主进程同步数据
3. **网络依赖**: 依赖 IPC 通信的稳定性

## 测试建议

### 功能测试
1. 验证资源缓存的正常获取和存储
2. 验证字体缓存的加载和解析
3. 验证缓存键的一致性
4. 验证初始化流程的稳定性

### 性能测试
1. 测试大量资源的缓存性能
2. 测试内存使用情况
3. 测试初始化时间

### 兼容性测试
1. 验证现有代码的兼容性
2. 验证 IPC 调用的稳定性
