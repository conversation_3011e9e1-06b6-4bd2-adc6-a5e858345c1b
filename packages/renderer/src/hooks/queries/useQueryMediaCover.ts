import { useQuery } from '@tanstack/react-query'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { ResourceModule } from '@/libs/request/api/resource'

/**
 * 获取媒体封面URL的查询hook
 * @param cover 封面对象ID
 * @param enabled 是否启用查询
 * @returns 查询结果，包含解析后的封面URL
 */
export const useQueryMediaCover = (cover?: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: [QUERY_KEYS.MEDIA_COVER, cover],
    queryFn: async () => {
      if (!cover) return '#'

      console.log(cover)
      try {
        const res = await ResourceModule.media.cover(cover)
        const doc = new DOMParser().parseFromString(res, 'text/html')
        return doc.querySelector('a')?.href ?? '#'
      } catch (error) {
        console.error('获取封面地址失败:', error)
        return '#'
      }
    },
    enabled: enabled && !!cover,
    staleTime: 5 * 60 * 1000, // 5分钟内不重新请求
    gcTime: 10 * 60 * 1000, // 10分钟后清理缓存
  })
}
