import { sha256sum } from './nodeCrypto.js'
import { versions } from './versions.js'
import { contextBridge, ipcRenderer } from 'electron'
import { createTypedIPCClient, exposeCrudable } from './ipc-client-utils.js'

const editor = createTypedIPCClient('editor')
contextBridge.exposeInMainWorld('editor', {
  generateCombos: (v: any) => editor.generateCombos(v),
  extractVideoKeyFrames: (v: any) => editor.extractVideoKeyFrames(v),
  compressEditorState: (v: any) => editor.compressEditorState(v),
  decompressEditorState: (v: any) => editor.decompressEditorState(v),
  uploadMixcutResult: (v: any) => editor.uploadMixcutResult(v),
  getAudioDuration: (v: any) => editor.getAudioDuration(v),
})

const resource = createTypedIPCClient('resource')
contextBridge.exposeInMainWorld('resource', {
  fetchOrSaveResource: (v: any) => resource.fetchOrSaveResource(v),
  cleanResource: (v: any) => resource.cleanResource(v),
  getResource: (v: any) => resource.getResource(v),
  getResourcePath: (v: any) => resource.getResourcePath(v),
  getAllResources: () => resource.getAllResources(),
})

const fileUploader = createTypedIPCClient('fileUploader')
contextBridge.exposeInMainWorld('fileUploader', {
  uploadBufferToOSS: (v: any) => fileUploader.uploadBufferToOSS(v),
})

const fileDownloader = createTypedIPCClient('fileDownloader')
contextBridge.exposeInMainWorld('fileDownloader', {
  selectFolder: (v: any) => fileDownloader.selectFolder(v),
  selectFile: (v: any) => fileDownloader.selectFile(v),
  fileExists: (v: any) => fileDownloader.fileExists(v),
  downloadFiles: (v: any) => fileDownloader.downloadFiles(v),
})

const baseInfo = createTypedIPCClient('baseInfo')
contextBridge.exposeInMainWorld('baseInfo', {
  setTenantId: (v: any) => baseInfo.setTenantId(v),
  setToken: (v: any) => baseInfo.setToken(v),
})

const windowClient = createTypedIPCClient('windowManager')
contextBridge.exposeInMainWorld('windowManager', {
  minimize: () => windowClient.minimize(),
  maximize: () => windowClient.maximize(),
  close: () => windowClient.close(),
})

//#region ~ Crudable Clients
const compose = createTypedIPCClient('compose')
contextBridge.exposeInMainWorld('compose', {
  ...exposeCrudable(compose),
  teamComposes: (v: any) => compose.teamComposes(v),
  scriptComposes: (v: any) => compose.scriptComposes(v),
  searchCompose: (v: any) => compose.searchCompose(v),
  updateDownloadStatus: (v: any) => compose.updateDownloadStatus(v),
  markAsRead: (v: any) => compose.markAsRead(v),
  getComposeStats: (v: any) => compose.getComposeStats(v),
})

const folder = createTypedIPCClient('folder')
contextBridge.exposeInMainWorld('folder', {
  ...exposeCrudable(folder),
  children: (v: any) => folder.children(v),
  path: (v: any) => folder.path(v),
  createDefaultStructure: (v: any) => folder.createDefaultStructure(v),
})

const materialFile = createTypedIPCClient('materialFile')
contextBridge.exposeInMainWorld('materialFile', {
  ...exposeCrudable(materialFile),
  findByHash: (v: any) => materialFile.findByHash(v),
  userMaterials: (v: any) => materialFile.userMaterials(v),
  byType: (v: any) => materialFile.byType(v),
  search: (v: any) => materialFile.search(v),
  updateStatus: (v: any) => materialFile.updateStatus(v),
  batchMove: (v: any) => materialFile.batchMove(v),
})

const task = createTypedIPCClient('task')
contextBridge.exposeInMainWorld('task', {
  ...exposeCrudable(task),
  userTasks: (v: any) => task.userTasks(v),
  folderTasks: (v: any) => task.folderTasks(v),
  tasksByType: (v: any) => task.tasksByType(v),
  search: (v: any) => task.search(v),
  updateStatus: (v: any) => task.updateStatus(v),
  batchMove: (v: any) => task.batchMove(v),
})
//#endregion

// 暴露 IPC 事件监听功能
contextBridge.exposeInMainWorld('electronAPI', {
  ipcRenderer: {
    on: (channel: string, callback: (...args: any[]) => void) => {
      ipcRenderer.on(channel, callback)
    },
    removeListener: (channel: string, callback: (...args: any[]) => void) => {
      ipcRenderer.removeListener(channel, callback)
    },
    removeAllListeners: (channel: string) => {
      ipcRenderer.removeAllListeners(channel)
    }
  }
})

function send(channel: string, message: string) {
  return ipcRenderer.invoke(channel, message)
}

export { sha256sum, versions, send }
