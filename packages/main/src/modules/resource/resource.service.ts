import { Injectable, OnModuleInit } from '@nestjs/common'
import {
  getEntry,
  initManifestManager,
  listAllEntries,
  removeEntry,
  setEntry,
  batchRemoveEntries
} from '@/infra/resource-cache/manifestManager.js'
import { ResourceCacheEntry, ResourceType } from '@app/shared/types/resource-cache.types.js'
import { getFileExtension, hashUrl, isCacheEntryExpired } from '@/infra/resource-cache/utils.js'
import { checkFileExists, ensureCacheDirExists, getLocalFilePath } from '@/infra/resource-cache/fileManager.js'
import fetch from 'node-fetch'
import { promises as fsPromises } from 'fs'
import path from 'path'
import { ResourceIPCClient } from '@app/shared/types/ipc/resource.js'

@Injectable()
export class ResourceService implements OnModuleInit, ResourceIPCClient {

  // 资源缓存配置
  private readonly defaultMaxAgeSeconds: number
  private readonly defaultMaxCacheSizeMB: number

  // 用于缓存文件存在性检查结果的内存缓存
  private fileExistsCache: Map<string, { exists: boolean, timestamp: number }>

  constructor() {
    this.defaultMaxAgeSeconds = 7 * 24 * 60 * 60 // 7 天
    this.defaultMaxCacheSizeMB = 1024 // 1 GB
    this.fileExistsCache = new Map()
  }

  /**
   * 初始化资源缓存服务
   */
  async onModuleInit() {
    try {
      // 初始化资源缓存系统
      await initManifestManager()

      // 执行文件完整性检查，确保 manifest 与实际文件系统状态一致
      await this.validateCacheIntegrity()

      // 启动时执行一次缓存清理
      this.cleanResource().catch(err => {
        console.error('[ResourceService] 初始化缓存清理失败:', err)
      })

      console.log('[ResourceService] 初始化完成')
    }
    catch (error) {
      console.error('[ResourceService] 初始化失败:', error)
      throw error
    }
  }

  async fetchOrSaveResource(props: {
    url: string;
    type: ResourceType;
    version?: string;
    customExt?: string
  }): Promise<string> {
    const { url, type, version = '1.0.0', customExt } = props

    if (!url || !type) {
      throw new Error('URL和资源类型不能为空')
    }

    const key = hashUrl(url)
    const ext = customExt || getFileExtension(url)

    const localPath = getLocalFilePath(type, key, ext)

    const entry = getEntry(key)

    if (entry) {
      // 更新访问时间
      entry.lastAccessed = Math.floor(Date.now() / 1000)
      await setEntry(entry)

      if (entry.version === version) {
        const fileExists = await this.checkFileExistsWithCache(entry.localPath)
        if (fileExists) {
          console.log(`Resource already cached and up-to-date: ${url}`)
          return entry.localPath
        } else {
          // 文件不存在但 manifest 中有记录，清理孤儿条目
          console.warn(`Resource file missing, cleaning orphaned entry: ${url} -> ${entry.localPath}`)
          await removeEntry(key)
        }
      } else {
        console.log(`Resource version mismatch, updating: ${url}`)
        // 版本不一致，需要重新下载
        await removeEntry(key) // 移除旧的缓存条目
      }
    }

    // 下载资源
    const { etag, size } = await this.downloadResource(url, localPath)

    // 字体文件直接使用下载的路径
    const finalLocalPath = localPath

    // 创建或更新缓存条目
    const newEntry: ResourceCacheEntry = {
      key,
      url,
      type,
      localPath: finalLocalPath, // 使用可能更新过的路径
      version,
      etag,
      lastAccessed: Math.floor(Date.now() / 1000),
      downloadedAt: Math.floor(Date.now() / 1000),
      size,
    }
    await setEntry(newEntry)

    // 更新文件存在性缓存
    this.fileExistsCache.set(finalLocalPath, { exists: true, timestamp: Date.now() })

    return finalLocalPath
  }

  async cleanResource(props?: { maxAgeSeconds?: number; maxCacheSizeMB?: number }): Promise<void> {
    const {
      maxAgeSeconds = this.defaultMaxAgeSeconds,
      maxCacheSizeMB = this.defaultMaxCacheSizeMB
    } = props || {}

    console.log('开始清理缓存...')
    const allEntries = listAllEntries()
    const entriesToDelete: ResourceCacheEntry[] = []
    let currentTotalSize = 0

    // 清空文件存在性缓存
    this.fileExistsCache.clear()

    for (const entry of allEntries) {
      // 计算当前总大小
      try {
        const fileExists = await checkFileExists(entry.localPath)
        if (fileExists) {
          const stats = await fsPromises.stat(entry.localPath)
          currentTotalSize += stats.size
        } else {
          console.warn(`文件 ${entry.localPath} 不存在，将删除对应 manifest 条目`)
          entriesToDelete.push(entry)
          continue // 跳过此条目，因为它可能已损坏
        }
      } catch (e) {
        console.warn(`无法获取文件 ${entry.localPath} 的大小，可能已损坏或丢失，将删除对应 manifest 条目:`, e)
        entriesToDelete.push(entry)
        continue // 跳过此条目，因为它可能已损坏
      }

      // 检查是否过期
      if (isCacheEntryExpired(entry, maxAgeSeconds)) {
        console.log(`Deleting expired entry: ${entry.url}`)
        entriesToDelete.push(entry)
      }
    }

    // 如果总大小超出限制，按照 LRU (最近最少使用) 策略删除
    const maxCacheSizeBytes = maxCacheSizeMB * 1024 * 1024
    if (currentTotalSize > maxCacheSizeBytes) {
      console.log(`Cache size (${(currentTotalSize / (1024 * 1024)).toFixed(2)} MB) exceeds limit (${maxCacheSizeMB} MB), applying LRU.`)
      // 按照 lastAccessed 升序排列 (最早访问的在前面)
      const sortedEntries = allEntries.sort((a, b) => a.lastAccessed - b.lastAccessed)

      for (const entry of sortedEntries) {
        if (currentTotalSize <= maxCacheSizeBytes) break // 已达到目标大小
        if (!entriesToDelete.includes(entry)) {
          console.log(`Deleting LRU entry: ${entry.url}`)
          entriesToDelete.push(entry)
          // 减去文件大小 (假设文件存在)
          try {
            const stats = await fsPromises.stat(entry.localPath)
            currentTotalSize -= stats.size
          } catch (e) {
            console.warn(`无法获取 LRU 文件 ${entry.localPath} 的大小，可能已损坏或丢失:`, e)
          }
        }
      }
    }

    // 执行删除操作
    for (const entry of entriesToDelete) {
      try {
        await fsPromises.unlink(entry.localPath) // 删除文件

        // 字体文件直接删除，无需特殊处理
        await removeEntry(entry.key) // 从 manifest 中移除
        console.log(`Successfully removed cached file: ${entry.localPath}`)
      } catch (error) {
        console.error(`Error deleting cached file ${entry.localPath}:`, error)
      }
    }
    console.log('缓存清理完成。')
  }

  getResource({ url }: { url: string }): Promise<ResourceCacheEntry | undefined> {
    if (!url) {
      throw new Error('URL不能为空')
    }

    const key = hashUrl(url)
    return Promise.resolve(getEntry(key))
  }

  async getResourcePath({ url, type }: { url: string; type: ResourceType }): Promise<string> {
    if (!url || !type) {
      throw new Error('URL和资源类型不能为空')
    }

    // 优先查找缓存条目，获取可能已经解压的路径
    const key = hashUrl(url)
    const entry = getEntry(key)

    // 如果找到缓存条目，且类型匹配，直接返回其路径
    if (entry && entry.type === type) {
      console.log(`[getResourceCachePath] 找到缓存条目: ${entry.localPath}`)
      return entry.localPath
    }

    // 如果没有缓存条目，或类型不匹配，生成默认路径
    const ext = getFileExtension(url)
    const defaultPath = getLocalFilePath(type, key, ext)

    // 字体文件直接使用默认路径，无需特殊处理

    console.log(`[getResourceCachePath] 未找到缓存条目，使用默认路径: ${defaultPath}`)
    return defaultPath
  }

  async getAllResources(): Promise<ResourceCacheEntry[]> {
    const allEntries = listAllEntries()
    const validEntries: ResourceCacheEntry[] = []
    const invalidKeys: string[] = []

    // 检查每个缓存条目对应的文件是否实际存在
    for (const entry of allEntries) {
      try {
        const fileExists = await this.checkFileExistsWithCache(entry.localPath)
        if (fileExists) {
          validEntries.push(entry)
        } else {
          console.warn(`缓存文件不存在，将从manifest中移除: ${entry.localPath}`)
          invalidKeys.push(entry.key)
        }
      } catch (e) {
        console.warn(`检查缓存文件是否存在时出错: ${entry.localPath}`, e)
        invalidKeys.push(entry.key)
      }
    }

    // 批量清理无效条目
    if (invalidKeys.length > 0) {
      await this.cleanInvalidEntries(invalidKeys)
    }

    return validEntries
  }

  /**
   * 批量清理无效的缓存条目
   * @param keys 缓存条目的key数组
   */
  private async cleanInvalidEntries(keys: string[]): Promise<void> {
    try {
      await batchRemoveEntries(keys)
      console.log(`已批量清理 ${keys.length} 个无效缓存条目`)
    } catch (error) {
      console.error('批量清理无效缓存条目失败，回退到逐个清理:', error)
      // 如果批量清理失败，回退到逐个清理
      for (const key of keys) {
        try {
          await removeEntry(key)
        } catch (e) {
          console.error(`清理缓存条目失败: ${key}`, e)
        }
      }
      console.log(`已逐个清理 ${keys.length} 个无效缓存条目`)
    }
  }

  /**
   * 带缓存的文件存在性检查，减少文件系统操作
   * @param filePath 文件路径
   * @returns 文件是否存在
   */
  private async checkFileExistsWithCache(filePath: string): Promise<boolean> {
    // 缓存有效期为5分钟
    const CACHE_TTL = 5 * 60 * 1000
    const now = Date.now()

    // 检查缓存
    const cached = this.fileExistsCache.get(filePath)
    if (cached && (now - cached.timestamp) < CACHE_TTL) {
      return cached.exists
    }

    // 缓存不存在或已过期，执行异步检查
    try {
      const exists = await checkFileExists(filePath)
      // 更新缓存
      this.fileExistsCache.set(filePath, { exists, timestamp: now })
      return exists
    } catch (error) {
      console.error(`检查文件是否存在时出错: ${filePath}`, error)
      // 出错时假设文件不存在
      this.fileExistsCache.set(filePath, { exists: false, timestamp: now })
      return false
    }
  }

  /**
   * 下载资源并保存到本地文件
   * @param url 资源 URL
   * @param localPath 本地保存路径
   * @returns 包含 size 的 Promise
   */
  private async downloadResource(url: string, localPath: string): Promise<{ etag: string; size: number }> {
    await ensureCacheDirExists(this.getLocalResourceType(localPath)) // 确保目录存在

    // 确保URL格式正确
    let finalUrl = url
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      finalUrl = `http://${url}`
      console.log(`URL格式调整: ${url} -> ${finalUrl}`)
    }

    try {
      const response = await fetch(finalUrl)

      if (response.status === 304) {
        const existingEntry = getEntry(hashUrl(url))
        if (existingEntry) {
          return { etag: existingEntry.etag || '', size: existingEntry.size }
        } else {
          throw new Error('ETag匹配但未找到现有条目.')
        }
      }

      if (!response.ok) {
        console.error(`下载失败: ${finalUrl}, 状态码: ${response.status}, 消息: ${response.statusText}`)
        throw new Error(`下载资源失败: ${response.statusText} (${response.status})`)
      }

      const buffer = await response.buffer()
      await fsPromises.writeFile(localPath, buffer)

      const etag = response.headers.get('etag') || ''
      const contentLength = response.headers.get('content-length')
      const size = contentLength ? parseInt(contentLength, 10) : buffer.length

      return { etag, size }
    } catch (error) {
      console.error(`下载资源时发生错误: ${finalUrl}`, error)
      throw new Error(`下载资源失败: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * 根据本地路径判断资源类型（辅助方法）
   * @param localPath 本地文件路径
   * @returns ResourceType
   */
  private getLocalResourceType(localPath: string): ResourceType {
    // 从路径中提取资源类型，这需要和 getLocalFilePath 的拼接方式对应
    const parts = localPath.split(path.sep)
    const resourceTypeString = parts[parts.length - 2] // 倒数第二个部分是类型文件夹名

    // 验证是否是有效的 ResourceType
    if (Object.values(ResourceType).includes(resourceTypeString as ResourceType)) {
      return resourceTypeString as ResourceType
    }
    // 默认或抛出错误，取决于你的设计
    console.warn(`无法从路径 ${localPath} 推断资源类型，默认为 AUDIO。`)
    return ResourceType.AUDIO // 或者抛出错误
  }

  /**
   * 验证缓存完整性
   * 检查 manifest.json 中记录的缓存条目是否与实际文件系统状态一致
   * 清理那些文件已被手动删除但仍在 manifest 中记录的孤儿条目
   */
  private async validateCacheIntegrity(): Promise<void> {
    console.log('[ResourceService] 开始验证缓存完整性...')

    const allEntries = listAllEntries()
    const invalidEntries: ResourceCacheEntry[] = []
    let validCount = 0
    let invalidCount = 0

    // 清空文件存在性缓存，确保检查的准确性
    this.fileExistsCache.clear()

    // 检查每个缓存条目对应的文件是否实际存在
    for (const entry of allEntries) {
      try {
        const fileExists = await checkFileExists(entry.localPath)
        if (!fileExists) {
          console.warn(`[ResourceService] 发现孤儿缓存条目: ${entry.url} -> ${entry.localPath}`)
          invalidEntries.push(entry)
          invalidCount++
        } else {
          validCount++
        }
      } catch (error) {
        console.error(`[ResourceService] 检查文件存在性时出错: ${entry.localPath}`, error)
        invalidEntries.push(entry)
        invalidCount++
      }
    }

    // 批量清理无效的缓存条目
    if (invalidEntries.length > 0) {
      console.log(`[ResourceService] 发现 ${invalidEntries.length} 个无效缓存条目，开始批量清理...`)

      try {
        const invalidKeys = invalidEntries.map(entry => entry.key)
        await batchRemoveEntries(invalidKeys)
        console.log(`[ResourceService] 已批量清理 ${invalidEntries.length} 个无效缓存条目`)
      } catch (error) {
        console.error('[ResourceService] 批量清理缓存条目失败:', error)
        // 如果批量清理失败，回退到逐个清理
        for (const entry of invalidEntries) {
          try {
            await removeEntry(entry.key)
            console.log(`[ResourceService] 已清理无效缓存条目: ${entry.url}`)
          } catch (error) {
            console.error(`[ResourceService] 清理缓存条目失败: ${entry.key}`, error)
          }
        }
      }
    }

    console.log(`[ResourceService] 缓存完整性验证完成 - 有效: ${validCount}, 无效已清理: ${invalidCount}`)
  }
}
