import { BaseIPCHandler } from '@/infra/types/BaseIPCHandler.js'
import { RequestService } from './request.service.js'
import { Inject } from '@nestjs/common'

export class BaseInfoIPCHandler extends BaseIPCHandler<'baseInfo'> {

  protected readonly platformPrefix = 'baseInfo'

  constructor(@Inject(RequestService) private readonly requestService: RequestService) {
    super()
  }

  /**
   * 注册所有 IPC 处理程序
   */
  registerAll(): void {
    this.registerHandler('setTenantId', this.requestService.setTenantId.bind(this.requestService))
    this.registerHandler('setToken', this.requestService.setToken.bind(this.requestService))
  }
}
